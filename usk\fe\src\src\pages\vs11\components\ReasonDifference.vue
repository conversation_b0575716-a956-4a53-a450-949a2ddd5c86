<template>
  <q-dialog v-model="isPopupReason" persistent>
    <q-card class="tw:max-w-[66.875rem] tw:tl:min-w-[47.625rem]">
      <div
          class="tw:text-m-design tw:font-bold tw:bg-[#004AB9] tw:text-white tw:px-6 tw:py-3"
        >
          入荷量の差異
        </div>
      <div class="tw:flex tw:flex-col tw:m-4 tw:text-[#333333]">
        <div
        class="tw:text-m-design tw:font-normal"
        >出荷量と入荷量の差異が大きすぎます。差異発生の理由をチェックしてください｡</div>
        <div class="tw:text-m-design tw:font-normal tw:grid tw:grid-cols-1
          tw:tl:grid-cols-3 tw:gap-4 tw:my-3">
          <div
          v-if="reasonInfo.displayShipment"
          class="tw:flex tw:justify-between tw:tl:justify-start">
            <div class="tw:mr-2">出荷量:</div>
            <div>
              {{
                reasonInfo.shipping_net_weight || 0
              }}
              g
            </div>
          </div>
          <div class="tw:flex tw:justify-between tw:tl:justify-start">
            <div class="tw:mr-2">入荷量:</div>
            <div>
              {{
                reasonInfo.arrivalNetWeight || 0
              }}
              g
            </div>
          </div>
        </div>
        <div
          class="tw:mb-1 tw:tl:my-3 tw:text-m-design
          tw:font-normal tw:relative"
        >
        差異の理由
        <q-badge
        class="tw:text-xxs-design tw:font-normal tw:px-[0.625rem]
        badgeColor tw:ml-2 tw:absolute tw:top-[0.7rem]"
        >
        必須
        </q-badge>
        </div>
        <div class="tw:mt-2 tw:tl:mt-4 tw:text-m-design">
          <q-radio size="xl" v-model="typeDiff" :val="TYPE_DIFFERENCE_WEIGHT_ENUM.DEATH" label="斃死" />
          <q-radio size="xl" v-model="typeDiff" :val="TYPE_DIFFERENCE_WEIGHT_ENUM.WEIGHT_ERROR" label="計量誤差" class="tw:px-3" />
          <q-radio size="xl" v-model="typeDiff" :val="TYPE_DIFFERENCE_WEIGHT_ENUM.OTHER" label="その他" />
        </div>
        <div v-if="errors.typeDiff" class="tw:ml-3 tw:text-[0.75rem] tw:tl:text-[1.15rem]
          tw:dt:text-[0.75rem] tw:text-red-error">
          {{ errors.typeDiff }}
        </div>
        <div
          class="tw:mb-1 tw:tl:my-3 tw:text-m-design
          tw:font-normal tw:relative"
        >
        「その他」を選択した理由
        <q-badge
        class="tw:text-xxs-design tw:font-normal tw:px-[0.625rem]
        badgeColor tw:ml-2 tw:absolute tw:top-[0.7rem]"
        >
        必須
        </q-badge>
        </div>
        <q-input v-model="reasonDiff" filled type="textarea" rows="4" maxlength="300" autocomplete="nope"
          :disable="isHiddenReason" :error="!!errors.reasonDiff" :error-message="errors.reasonDiff" no-error-icon input-class="tw:text-m-design" />
        <div class="tw:tl:flex tw:mt-6 tw:gap-4 tw:w-full tw:tl:justify-end
          tw:flex tw:justify-center tw:flex-col tw:tl:flex-row">
          <BaseButton
            outline
            padding="1.25rem"
            class="tw:rounded-[40px] tw:w-full"
            :class="`tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:font-bold
            tw:tl:w-[19.5rem] tw:tl:h-[5.5rem]
            tw:w-full`"
            label="入荷量を修正する"
            @click.prevent="closeModal"
          />
          <BaseButton
            outline
            padding="1.25rem"
            class="tw:rounded-[40px] tw:w-full"
            :class="`tw:bg-[#004AB9] tw:text-white tw:text-m-design tw:tl:font-bold
            tw:tl:w-[24.5rem] tw:tl:h-[5.5rem]
            tw:w-full`" label="差異の理由を登録する" @click.prevent="acceptModal" />
        </div>
      </div>
    </q-card>
  </q-dialog>
</template>

<script setup>
// #region import
import { ref, inject, watch, onMounted } from 'vue';
import { TYPE_DIFFERENCE_WEIGHT_ENUM } from 'helpers/constants';
import reasonDifferenceArrivalSchema from 'schemas/reasonDifferenceArrival';
import useValidate from 'composables/validate';
import BaseButton from 'components/base/vs/BaseButton.vue';
// import { FORMAT_NUMBER_WEIGHT } from 'src/helpers/common';

// #endregion import

// #region variable
const isPopupReason = inject('isPopupReason');
const typeDiff = inject('typeDiff');
const reasonDiff = inject('reasonDiff');
const reasonInfo = inject('reasonInfo');
const confirmReason = inject('confirmReason');
const { errors, validateData } = useValidate();

const isHiddenReason = ref(true);
// #endregion variable

// #region function
const closeModal = () => {
  isPopupReason.value = false;
};

const acceptModal = () => {
  const dataReason = {
    typeDiff: typeDiff.value,
  };
  if (+typeDiff.value === TYPE_DIFFERENCE_WEIGHT_ENUM.OTHER) {
    dataReason.reasonDiff = reasonDiff.value;
  }
  const valid = validateData(reasonDifferenceArrivalSchema, dataReason);
  if (!valid) {
    return;
  }
  isPopupReason.value = false;
  confirmReason();
};
// #endregion function

// watch
watch(typeDiff, value => {
  if (+value === TYPE_DIFFERENCE_WEIGHT_ENUM.OTHER) {
    isHiddenReason.value = false;
  } else {
    isHiddenReason.value = true;
    reasonDiff.value = '';
  }
});

watch(isPopupReason, () => {
  errors.value = {};
});
</script>

<style scoped>
.badgeColor {
  background-color: #E80F00 !important;
  color: #ffffff !important;
}
</style>
